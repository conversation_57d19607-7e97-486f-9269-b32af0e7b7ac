_format_version: "3.0"
_konnect:
  control_plane_name: summit
plugins:
- config:
    logging:
      log_payloads: true
      log_statistics: true
    max_request_body_size: 16384
    mode: conversion-listener
    server: null
    tools:
    - description: Retrieve a random chuck joke in JSON format.
      method: GET
      path: /api/chucknorris/random
      parameters:
      - name: category
        in: query
        description: Retrieve a random chuck norris joke from a given category.
        required: false
        schema:
            type: string
      annotations:
        "title": "Chuck Norris Random Joke"
  enabled: true
  name: ai-mcp-proxy
  protocols:
  - grpc
  - grpcs
  - http
  - https
  route: jokes-mcp
  service: chucknorris-service
services:
- connect_timeout: 60000
  enabled: true
  host: api.chucknorris.io
  name: chucknorris-service
  path: /jokes
  port: 443
  protocol: https
  read_timeout: 60000
  retries: 5
  routes:
  - https_redirect_status_code: 426
    name: jokes-mcp
    path_handling: v0
    paths:
    - /mcp/chucknorris
    preserve_host: false
    protocols:
    - http
    - https
    regex_priority: 0
    request_buffering: true
    response_buffering: true
    strip_path: true
  - https_redirect_status_code: 426
    name: chucknorris-route
    path_handling: v0
    paths:
    - /api/chucknorris
    preserve_host: false
    protocols:
    - http
    - https
    regex_priority: 0
    request_buffering: true
    response_buffering: true
    strip_path: true
  write_timeout: 60000
