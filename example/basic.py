from openai import OpenAI

# Configure the client for Kong AI Gateway
client = OpenAI(
    base_url="https://ai-gateway.kong-sales-engineering.com",
    default_headers={
        "x-provider": "bedrock",
        "x-model": "anthropic.claude-3-haiku-20240307-v1:0"
    }
)

# Make your first chat completion request
completion = client.chat.completions.create(
    messages=[
        {"role": "user", "content": "Hello! How are you today?"}
    ]
)

# Print the response
print(completion.choices[0].message.content)
